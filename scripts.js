document.addEventListener('DOMContentLoaded', function() {
    // 初始化日期时间选择器
    initDateTimePickers();

    // 绑定媒体查看器事件
    bindMediaViewerEvents();

    // 绑定音频播放按钮事件
    bindAudioPlayEvents();

    // 绑定时间线展开/折叠事件
    bindTimelineEvents();

    // 绑定旅客头像点击事件
    bindPassengerAvatarEvents();

    // 初始化时间线状态（默认展开第一个）
    initTimelineState();

    // 绑定导航事件
    bindNavigationEvents();
});

function initDateTimePickers() {
    // 设置默认时间范围为当天
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    document.getElementById('start-time').value = `${todayStr}T00:00`;
    document.getElementById('end-time').value = `${todayStr}T23:59`;

    // 日期选择器变化时更新时间范围
    document.getElementById('date-select').addEventListener('change', function(e) {
        const value = e.target.value;
        const today = new Date();

        if (value === 'today') {
            const todayStr = today.toISOString().split('T')[0];
            document.getElementById('start-time').value = `${todayStr}T00:00`;
            document.getElementById('end-time').value = `${todayStr}T23:59`;
        } else if (value === 'yesterday') {
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            document.getElementById('start-time').value = `${yesterdayStr}T00:00`;
            document.getElementById('end-time').value = `${yesterdayStr}T23:59`;
        }
    });
}

function bindMediaViewerEvents() {
    // 获取所有媒体缩略图
    const mediaThumbnails = document.querySelectorAll('.media-thumbnail');
    const modal = document.getElementById('media-viewer');
    const closeBtn = modal.querySelector('.close');
    const mediaContainer = modal.querySelector('.media-container');

    // 为每个缩略图添加点击事件
    mediaThumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const mediaType = this.dataset.type; // 'image', 'video', 'xray'
            const mediaUrl = this.dataset.url;

            // 清空并添加新的媒体内容
            mediaContainer.innerHTML = '';

            if (mediaType === 'video') {
                const video = document.createElement('video');
                video.src = mediaUrl;
                video.controls = true;
                video.autoplay = true;
                mediaContainer.appendChild(video);
            } else {
                const img = document.createElement('img');
                img.src = mediaUrl;
                mediaContainer.appendChild(img);
            }

            // 显示模态框
            modal.style.display = 'block';
        });
    });

    // 关闭按钮事件
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function bindAudioPlayEvents() {
    // 获取所有音频播放按钮
    const audioButtons = document.querySelectorAll('.audio-play');

    audioButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 模拟音频播放（因为没有实际音频文件）
            this.style.backgroundColor = '#28a745';
            this.innerHTML = '<span class="audio-icon">🔊</span><span>正在播放...</span>';

            // 2秒后恢复原状
            setTimeout(() => {
                this.style.backgroundColor = '';
                this.innerHTML = '<span class="audio-icon">🔊</span><span>人证合一验证一致!</span>';
            }, 2000);

            // 如果有实际音频文件，可以使用以下代码
            // const audioUrl = this.dataset.audio;
            // const audio = new Audio(audioUrl);
            // audio.play().catch(e => console.log('音频播放失败:', e));
        });
    });
}

function bindTimelineEvents() {
    // 获取所有时间线项目标题
    const timelineHeaders = document.querySelectorAll('.timeline-item-header');

    timelineHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const timelineItem = this.parentElement;
            const content = this.nextElementSibling;
            const icon = this.querySelector('.timeline-icon');

            // 切换内容显示/隐藏
            if (timelineItem.classList.contains('expanded')) {
                timelineItem.classList.remove('expanded');
                content.style.maxHeight = '0';
                icon.textContent = '▶';
            } else {
                timelineItem.classList.add('expanded');
                content.style.maxHeight = content.scrollHeight + 'px';
                icon.textContent = '▼';
            }
        });
    });
}

function bindPassengerAvatarEvents() {
    const avatar = document.getElementById('passenger-avatar');
    const modal = document.getElementById('media-viewer');
    const mediaContainer = modal.querySelector('.media-container');

    avatar.addEventListener('click', function() {
        // 显示旅客头像的大图
        mediaContainer.innerHTML = '';
        const img = document.createElement('img');
        img.src = this.src;
        img.alt = '旅客头像';
        mediaContainer.appendChild(img);
        modal.style.display = 'block';
    });
}

function initTimelineState() {
    // 默认展开第一个时间线项目
    const firstTimelineItem = document.querySelector('.timeline-item');
    if (firstTimelineItem) {
        const content = firstTimelineItem.querySelector('.timeline-item-content');
        const icon = firstTimelineItem.querySelector('.timeline-icon');

        firstTimelineItem.classList.add('expanded');
        content.style.maxHeight = content.scrollHeight + 'px';
        icon.textContent = '▼';
    }
}

function bindNavigationEvents() {
    // 绑定搜索按钮事件
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // 模拟搜索功能
            console.log('执行搜索...');

            // 可以在这里添加实际的搜索逻辑
            const startTime = document.getElementById('start-time').value;
            const endTime = document.getElementById('end-time').value;
            const sortBy = document.getElementById('sort-by').value;

            console.log('搜索参数:', {
                startTime,
                endTime,
                sortBy
            });

            // 显示搜索反馈
            this.style.backgroundColor = '#28a745';
            this.textContent = '搜索中...';

            setTimeout(() => {
                this.style.backgroundColor = '';
                this.textContent = '查询过检信息';
            }, 1000);
        });
    }

    // 绑定详情按钮事件
    const detailBtn = document.querySelector('.detail-btn');
    if (detailBtn) {
        detailBtn.addEventListener('click', function() {
            alert('显示旅客详细信息');
        });
    }

    // 绑定排序选择器事件
    const sortSelect = document.getElementById('sort-by');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            console.log('排序方式改变:', sortValue);

            // 这里可以添加实际的排序逻辑
            // 例如重新排列时间线项目
        });
    }
}