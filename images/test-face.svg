<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="200" height="200" fill="url(#bg)" rx="20"/>
  
  <!-- 头部轮廓 -->
  <ellipse cx="100" cy="80" rx="45" ry="55" fill="#fff" opacity="0.9"/>
  
  <!-- 眼睛 -->
  <circle cx="85" cy="70" r="4" fill="#333"/>
  <circle cx="115" cy="70" r="4" fill="#333"/>
  
  <!-- 鼻子 -->
  <path d="M100 80 L95 90 L100 92 L105 90 Z" fill="#ddd"/>
  
  <!-- 嘴巴 -->
  <path d="M90 100 Q100 110 110 100" stroke="#333" stroke-width="2" fill="none"/>
  
  <!-- 身体 -->
  <path d="M50 150 Q50 130 100 130 Q150 130 150 150 L150 200 L50 200 Z" fill="#fff" opacity="0.9"/>
  
  <!-- 标签 -->
  <text x="100" y="180" font-family="Arial, sans-serif" font-size="14" fill="#fff" text-anchor="middle" font-weight="bold">测试头像</text>
</svg>
