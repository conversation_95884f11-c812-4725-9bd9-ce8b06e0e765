<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航空货运代理系统 - 产品介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
            color: white;
            overflow-x: hidden;
        }

        .presentation-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            display: none;
            padding: 40px;
            position: relative;
            background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .slide-title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide-subtitle {
            font-size: 1.5em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .slide-content {
            max-width: 1200px;
            width: 100%;
            display: flex;
            gap: 40px;
            align-items: center;
        }

        .content-left, .content-right {
            flex: 1;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .feature-description {
            font-size: 1.1em;
            line-height: 1.6;
            opacity: 0.9;
        }

        .system-image {
            width: 100%;
            max-width: 600px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .value-list {
            list-style: none;
            padding: 0;
        }

        .value-item {
            background: rgba(255, 255, 255, 0.1);
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #ffa726;
            backdrop-filter: blur(10px);
        }

        .value-item h3 {
            font-size: 1.3em;
            margin-bottom: 10px;
            color: #ffa726;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 25px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .slide-number {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .logo {
            position: absolute;
            top: 30px;
            left: 30px;
            font-size: 1.5em;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .highlight {
            color: #ffa726;
            font-weight: bold;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            min-width: 200px;
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #ffa726;
            display: block;
        }

        .stat-label {
            font-size: 1.2em;
            margin-top: 10px;
            opacity: 0.9;
        }

        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .process-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            flex: 1;
            min-width: 150px;
            backdrop-filter: blur(10px);
            position: relative;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2em;
            color: #ffa726;
        }

        .process-step:last-child::after {
            display: none;
        }

        .module-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-3px);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide.active .slide-content {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <div class="logo">
                ✈️ 航空货运代理系统
            </div>
            <div class="slide-number">1/7</div>
            <div class="slide-header">
                <h1 class="slide-title">航空货运代理系统</h1>
                <p class="slide-subtitle">全流程信息化管理 · 提质增效 · 智能化运营</p>
                <p class="slide-subtitle">让货运业务更高效，让管理决策更智能</p>
            </div>
            <div class="stats-container">
                <div class="stat-item">
                    <span class="stat-number">全流程</span>
                    <span class="stat-label">业务覆盖</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">电子化</span>
                    <span class="stat-label">单证管理</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">智能化</span>
                    <span class="stat-label">数据分析</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">标准化</span>
                    <span class="stat-label">业务操作</span>
                </div>
            </div>
        </div>

        <!-- 第2页：系统概述 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">2/7</div>
            <div class="slide-header">
                <h2 class="slide-title">系统概述</h2>
                <p class="slide-subtitle">航空货站全流程信息化管理解决方案</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMEY0Qzc1IiByeD0iMTUiLz4KPHJlY3QgeD0iNTAiIHk9IjUwIiB3aWR0aD0iNTAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiByeD0iMTAiLz4KPHN2ZyB4PSIxMDAiIHk9IjEwMCIgd2lkdGg9IjQwMCIgaGVpZ2h0PSIyMDAiPgo8dGV4dCB4PSIyMDAiIHk9IjUwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+6Iiq56m66LSn6L+Q5Luj55CG57O757ufPC90ZXh0Pgo8Y2lyY2xlIGN4PSI4MCIgY3k9IjEyMCIgcj0iMjUiIGZpbGw9IiNGRkE3MjYiLz4KPHRleHQgeD0iODAiIHk9IjEyNSIgZmlsbD0iIzBGNEM3NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+6L+Q5Lu3PC90ZXh0Pgo8Y2lyY2xlIGN4PSIyMDAiIGN5PSIxMjAiIHI9IjI1IiBmaWxsPSIjRkZBNzI2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTI1IiBmaWxsPSIjMEY0Qzc1IiBmb250LXNpemU9IjEyIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ljLblgZU8L3RleHQ+CjxjaXJjbGUgY3g9IjMyMCIgY3k9IjEyMCIgcj0iMjUiIGZpbGw9IiNGRkE3MjYiLz4KPHRleHQgeD0iMzIwIiB5PSIxMjUiIGZpbGw9IiMwRjRDNzUiIGZvbnQtc2l6ZT0iMTIiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuS7k+W6kzwvdGV4dD4KPGxpbmUgeDE9IjEwNSIgeTE9IjEyMCIgeDI9IjE3NSIgeTI9IjEyMCIgc3Ryb2tlPSIjRkZBNzI2IiBzdHJva2Utd2lkdGg9IjMiLz4KPGxpbmUgeDE9IjIyNSIgeTE9IjEyMCIgeDI9IjI5NSIgeTI9IjEyMCIgc3Ryb2tlPSIjRkZBNzI2IiBzdHJva2Utd2lkdGg9IjMiLz4KPC9zdmc+CjwvdGV4dD4KPC9zdmc+" alt="系统架构图" class="system-image">
                </div>
                <div class="content-right">
                    <div class="feature-description">
                        <h3 style="font-size: 2em; margin-bottom: 20px; color: #ffa726;">🚀 全流程信息化管理</h3>
                        <p style="font-size: 1.3em; line-height: 1.8; margin-bottom: 20px;">
                            覆盖<span class="highlight">收运发货、制单、物品管理、中转发货、进港货物提取</span>等全业务流程
                        </p>
                        <p style="font-size: 1.3em; line-height: 1.8; margin-bottom: 20px;">
                            针对<span class="highlight">中小型机场货站</span>提供的专业生产系统，实现业务的<span class="highlight">电子化、在线化</span>
                        </p>
                        <p style="font-size: 1.3em; line-height: 1.8;">
                            通过<span class="highlight">标准化业务操作</span>和<span class="highlight">智能化数据分析</span>，提升整体业务运转效率，达到提质增效的目标
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第3页：业务流程 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">3/7</div>
            <div class="slide-header">
                <h2 class="slide-title">业务流程覆盖</h2>
                <p class="slide-subtitle">从收运到提货的完整业务链条</p>
            </div>
            <div class="slide-content" style="flex-direction: column;">
                <div class="process-flow">
                    <div class="process-step">
                        <div class="feature-icon">📦</div>
                        <h3>收运发货</h3>
                        <p>货物接收<br>运单制作</p>
                    </div>
                    <div class="process-step">
                        <div class="feature-icon">📋</div>
                        <h3>制单管理</h3>
                        <p>电子运单<br>单证打印</p>
                    </div>
                    <div class="process-step">
                        <div class="feature-icon">🏪</div>
                        <h3>仓储管理</h3>
                        <p>入库出库<br>库存盘点</p>
                    </div>
                    <div class="process-step">
                        <div class="feature-icon">🔄</div>
                        <h3>中转发货</h3>
                        <p>货物中转<br>关封管理</p>
                    </div>
                    <div class="process-step">
                        <div class="feature-icon">✈️</div>
                        <h3>进港提取</h3>
                        <p>货物理货<br>提货处理</p>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <span class="feature-icon">💰</span>
                        <h3 class="feature-title">运价管理</h3>
                        <p class="feature-description">
                            运价维护、运价计算与查询，支持多种计费模式和价格策略
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">👥</span>
                        <h3 class="feature-title">客户管理</h3>
                        <p class="feature-description">
                            客户档案管理、客户分级管理，建立完善的客户关系体系
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <h3 class="feature-title">数据分析</h3>
                        <p class="feature-description">
                            业务数据统计、对账单统计、经营分析，支持多维度报表
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚠️</span>
                        <h3 class="feature-title">异常管理</h3>
                        <p class="feature-description">
                            不正常货物台账登记、危险品管理，确保货运安全
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页：核心功能模块 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">4/7</div>
            <div class="slide-header">
                <h2 class="slide-title">核心功能模块</h2>
                <p class="slide-subtitle">九大核心模块，全面支撑货运业务</p>
            </div>
            <div class="slide-content" style="flex-direction: column;">
                <div class="module-grid">
                    <div class="module-card">
                        <div class="feature-icon">💲</div>
                        <h3>运价管理</h3>
                        <p>运价维护、计算查询<br>多种计费策略</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">🎫</div>
                        <h3>票证管理</h3>
                        <p>电子托书、电子运单<br>电子安检申报单</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">📝</div>
                        <h3>订单管理</h3>
                        <p>订单填写、运单开单<br>费用计算</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">🖨️</div>
                        <h3>单据打印</h3>
                        <p>运单打印<br>货运舱单打印</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">📦</div>
                        <h3>库存管理</h3>
                        <p>出入库操作<br>仓库盘点</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">📈</div>
                        <h3>统计分析</h3>
                        <p>业务数据统计<br>经营分析报表</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">👤</div>
                        <h3>客户管理</h3>
                        <p>客户档案<br>分级管理</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">✈️</div>
                        <h3>航班管理</h3>
                        <p>航班计划管理<br>航班查看</p>
                    </div>
                    <div class="module-card">
                        <div class="feature-icon">⚠️</div>
                        <h3>异常管理</h3>
                        <p>不正常货物台账<br>危险品管理</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：电子化单证管理 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">5/7</div>
            <div class="slide-header">
                <h2 class="slide-title">电子化单证管理</h2>
                <p class="slide-subtitle">全面实现业务操作标准化和电子化</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <ul class="value-list">
                        <li class="value-item">
                            <h3>📋 电子托书</h3>
                            <p>数字化托运书管理，提高处理效率，减少纸质单据使用</p>
                        </li>
                        <li class="value-item">
                            <h3>🛡️ 电子安检申报单</h3>
                            <p>安检信息电子化申报，确保货物安全，提升安检效率</p>
                        </li>
                        <li class="value-item">
                            <h3>📄 电子运单</h3>
                            <p>运单信息电子化管理，支持在线填写、审核、打印</p>
                        </li>
                        <li class="value-item">
                            <h3>💰 电子对账</h3>
                            <p>自动化对账功能，减少人工错误，提高财务处理效率</p>
                        </li>
                    </ul>
                </div>
                <div class="content-right">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDUwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iNDAwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIHJ4PSIxNSIvPgo8cmVjdCB4PSI1MCIgeT0iNTAiIHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMikiIHJ4PSIxMCIvPgo8dGV4dCB4PSIyNTAiIHk9IjEwMCIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMjQiIHRleHQtYW5jaG9yPSJtaWRkbGUiPueUteWtkOWMluWNleivgeeuoeeQhjwvdGV4dD4KPHJlY3QgeD0iMTAwIiB5PSIxNDAiIHdpZHRoPSIzMDAiIGhlaWdodD0iNDAiIGZpbGw9IiNGRkE3MjYiIHJ4PSI1Ii8+Cjx0ZXh0IHg9IjI1MCIgeT0iMTY1IiBmaWxsPSIjMEY0Qzc1IiBmb250LXNpemU9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7nlLXlrZDmiZPljaE8L3RleHQ+CjxyZWN0IHg9IjEwMCIgeT0iMjAwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkZBNzI2IiByeD0iNSIvPgo8dGV4dCB4PSIyNTAiIHk9IjIyNSIgZmlsbD0iIzBGNEM3NSIgZm9udC1zaXplPSIxNiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+55S15a2Q6L+Q5Y2VPC90ZXh0Pgo8cmVjdCB4PSIxMDAiIHk9IjI2MCIgd2lkdGg9IjMwMCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0ZGQTcyNiIgcng9IjUiLz4KPHRleHQgeD0iMjUwIiB5PSIyODUiIGZpbGw9IiMwRjRDNzUiIGZvbnQtc2l6ZT0iMTYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPueUteWtkOWvuei0pTwvdGV4dD4KPC9zdmc+" alt="电子化单证" class="system-image">
                </div>
            </div>
        </div>

        <!-- 第6页：系统优势 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">6/7</div>
            <div class="slide-header">
                <h2 class="slide-title">系统优势</h2>
                <p class="slide-subtitle">专为中小型机场货站量身定制</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <div class="stats-container" style="flex-direction: column; gap: 20px;">
                        <div class="stat-item">
                            <span class="stat-number">全流程</span>
                            <span class="stat-label">业务覆盖</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">标准化</span>
                            <span class="stat-label">操作流程</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">智能化</span>
                            <span class="stat-label">数据分析</span>
                        </div>
                    </div>
                </div>
                <div class="content-right">
                    <ul class="value-list">
                        <li class="value-item">
                            <h3>🚀 提质增效</h3>
                            <p>通过信息化手段，显著提升货运业务处理效率，减少人工操作错误</p>
                        </li>
                        <li class="value-item">
                            <h3>📱 在线化管理</h3>
                            <p>实现业务全流程在线化操作，支持远程办公和移动端访问</p>
                        </li>
                        <li class="value-item">
                            <h3>🔗 系统集成</h3>
                            <p>与地面服务单位无缝对接，实现数据共享和业务协同</p>
                        </li>
                        <li class="value-item">
                            <h3>📊 智能决策</h3>
                            <p>提供丰富的数据统计和分析功能，为经营决策提供数据支撑</p>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第7页：联系我们 -->
        <div class="slide">
            <div class="logo">✈️ 航空货运代理系统</div>
            <div class="slide-number">7/7</div>
            <div class="slide-header">
                <h2 class="slide-title">携手共建智慧货站</h2>
                <p class="slide-subtitle">专业解决方案 · 贴心服务 · 持续创新</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <div class="feature-card" style="text-align: center; padding: 40px;">
                        <h3 style="font-size: 2em; margin-bottom: 30px; color: #ffa726;">🤝 服务优势</h3>
                        <div style="text-align: left;">
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>专业团队</strong>：拥有丰富的航空货运系统开发经验</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>成熟方案</strong>：针对中小型机场货站的专业解决方案</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>快速部署</strong>：标准化产品，快速上线投入使用</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>持续服务</strong>：提供全方位的技术支持和维护服务</p>
                        </div>
                    </div>
                </div>
                <div class="content-right">
                    <div class="feature-card" style="text-align: center; padding: 40px;">
                        <h3 style="font-size: 2em; margin-bottom: 30px; color: #ffa726;">📞 联系方式</h3>
                        <div style="text-align: left; font-size: 1.2em;">
                            <p style="margin-bottom: 20px;">
                                <strong>销售咨询：</strong><br>
                                400-XXX-XXXX
                            </p>
                            <p style="margin-bottom: 20px;">
                                <strong>技术支持：</strong><br>
                                <EMAIL>
                            </p>
                            <p style="margin-bottom: 20px;">
                                <strong>商务合作：</strong><br>
                                <EMAIL>
                            </p>
                            <p>
                                <strong>公司地址：</strong><br>
                                北京市朝阳区航空港大厦XX层
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 1.5em; color: #ffa726; font-weight: bold;">
                    让我们一起构建更高效、更智能的航空货运管理平台！
                </p>
            </div>
        </div>

        <!-- 导航控制 -->
        <div class="navigation">
            <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
            <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
            <button class="nav-btn" onclick="toggleFullscreen()">全屏</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 触摸控制（移动设备）
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        }

        // 自动播放功能（可选）
        let autoPlay = false;
        let autoPlayInterval;

        function startAutoPlay() {
            if (autoPlay) return;
            autoPlay = true;
            autoPlayInterval = setInterval(nextSlide, 10000); // 10秒自动切换
        }

        function stopAutoPlay() {
            autoPlay = false;
            clearInterval(autoPlayInterval);
        }

        // 点击任意位置停止自动播放
        document.addEventListener('click', stopAutoPlay);
        document.addEventListener('keydown', stopAutoPlay);

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里启动自动播放
            // startAutoPlay();
        });
    </script>
</body>
</html>