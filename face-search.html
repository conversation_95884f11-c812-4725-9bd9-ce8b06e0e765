<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人脸比对查询系统</title>
    <link rel="stylesheet" href="face-search.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h1>🔍 人脸比对查询系统</h1>
                <nav style="display: flex; gap: 1rem;">
                    <a href="index.html" style="color: white; text-decoration: none; padding: 0.5rem 1rem; border-radius: 4px; background: rgba(255,255,255,0.2);">安检回查</a>
                    <a href="face-search.html" style="color: white; text-decoration: none; padding: 0.5rem 1rem; border-radius: 4px; background: rgba(255,255,255,0.3);">人脸比对</a>
                </nav>
            </div>
            <div class="header-info">
                <span>智能识别 • 快速查询 • 精准匹配</span>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- 左侧上传区域 -->
        <div class="upload-section">
            <div class="upload-card">
                <h2>📷 上传查询照片</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-placeholder" id="uploadPlaceholder">
                        <div class="upload-icon">📁</div>
                        <p>点击或拖拽上传照片</p>
                        <p class="upload-hint">支持 JPG、PNG 格式</p>
                    </div>
                    <img id="uploadedImage" class="uploaded-image" style="display: none;" alt="上传的照片">
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>

                <div class="upload-actions">
                    <button id="uploadBtn" class="btn btn-primary">选择照片</button>
                    <button id="clearBtn" class="btn btn-secondary" style="display: none;">清除照片</button>
                </div>

                <div class="search-controls">
                    <div class="control-group">
                        <label>相似度阈值:</label>
                        <input type="range" id="similarityThreshold" min="60" max="99" value="85" class="slider">
                        <span id="thresholdValue">85%</span>
                    </div>
                    <div class="control-group">
                        <label>搜索范围:</label>
                        <select id="searchScope" class="select">
                            <option value="all">全部数据库</option>
                            <option value="recent">近期记录</option>
                            <option value="vip">重点人员</option>
                            <option value="blacklist">黑名单</option>
                        </select>
                    </div>
                    <button id="searchBtn" class="btn btn-search" disabled>🔍 开始比对查询</button>
                </div>
            </div>

            <!-- 查询状态 -->
            <div class="status-card" id="statusCard" style="display: none;">
                <div class="status-content">
                    <div class="status-icon" id="statusIcon">⏳</div>
                    <div class="status-text" id="statusText">准备查询...</div>
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧结果区域 -->
        <div class="results-section">
            <div class="results-header">
                <h2>🎯 查询结果</h2>
                <div class="results-stats" id="resultsStats" style="display: none;">
                    <span>找到 <strong id="matchCount">0</strong> 条匹配记录</span>
                    <span>用时 <strong id="searchTime">0</strong> 秒</span>
                </div>
            </div>

            <div class="results-container" id="resultsContainer">
                <div class="no-results" id="noResults">
                    <div class="no-results-icon">🔍</div>
                    <p>请上传照片开始查询</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>👤 人员详细信息</h3>
                <span class="close" id="closeModal">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细信息将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script src="face-search.js"></script>
</body>
</html>
