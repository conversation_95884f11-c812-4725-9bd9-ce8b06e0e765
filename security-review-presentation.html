<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机场安检回查系统 - 产品介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow-x: hidden;
        }

        .presentation-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            display: none;
            padding: 40px;
            position: relative;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .slide-title {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide-subtitle {
            font-size: 1.5em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .slide-content {
            max-width: 1200px;
            width: 100%;
            display: flex;
            gap: 40px;
            align-items: center;
        }

        .content-left, .content-right {
            flex: 1;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .feature-description {
            font-size: 1.1em;
            line-height: 1.6;
            opacity: 0.9;
        }

        .system-image {
            width: 100%;
            max-width: 600px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .value-list {
            list-style: none;
            padding: 0;
        }

        .value-item {
            background: rgba(255, 255, 255, 0.1);
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #00ff88;
            backdrop-filter: blur(10px);
        }

        .value-item h3 {
            font-size: 1.3em;
            margin-bottom: 10px;
            color: #00ff88;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 25px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .slide-number {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .logo {
            position: absolute;
            top: 30px;
            left: 30px;
            font-size: 1.5em;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .highlight {
            color: #00ff88;
            font-weight: bold;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            min-width: 200px;
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #00ff88;
            display: block;
        }

        .stat-label {
            font-size: 1.2em;
            margin-top: 10px;
            opacity: 0.9;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide.active .slide-content {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <div class="logo">
                ✈️ 机场安检管控平台
            </div>
            <div class="slide-number">1/6</div>
            <div class="slide-header">
                <h1 class="slide-title">智能安检回查系统</h1>
                <p class="slide-subtitle">全流程可追溯 · 快速精准定位 · 智能化管控</p>
                <p class="slide-subtitle">让安检管理更智能，让安全保障更可靠</p>
            </div>
            <div class="stats-container">
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">全流程覆盖</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">秒级</span>
                    <span class="stat-label">快速检索</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">全天候监控</span>
                </div>
            </div>
        </div>

        <!-- 第2页：系统概述 -->
        <div class="slide">
            <div class="logo">✈️ 机场安检管控平台</div>
            <div class="slide-number">2/6</div>
            <div class="slide-header">
                <h2 class="slide-title">系统概述</h2>
                <p class="slide-subtitle">集成化、智能化的安检回查解决方案</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMUUzQzcyIiByeD0iMTUiLz4KPHJlY3QgeD0iNTAiIHk9IjUwIiB3aWR0aD0iNTAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIiByeD0iMTAiLz4KPHN2ZyB4PSIxMDAiIHk9IjEwMCIgd2lkdGg9IjQwMCIgaGVpZ2h0PSIyMDAiPgo8dGV4dCB4PSIyMDAiIHk9IjUwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5a6J5qOA5Zue5p+l57O757ufPC90ZXh0Pgo8Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjMDBGRjg4Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTA1IiBmaWxsPSIjMUUzQzcyIiBmb250LXNpemU9IjE0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ml4fkurrjgIE8L3RleHQ+CjxjaXJjbGUgY3g9IjIwMCIgY3k9IjEwMCIgcj0iMzAiIGZpbGw9IiMwMEZGODgiLz4KPHRleHQgeD0iMjAwIiB5PSIxMDUiIGZpbGw9IiMxRTNDNzIiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWbvuWDjzwvdGV4dD4KPGNpcmNsZSBjeD0iMzAwIiBjeT0iMTAwIiByPSIzMCIgZmlsbD0iIzAwRkY4OCIvPgo8dGV4dCB4PSIzMDAiIHk9IjEwNSIgZmlsbD0iIzFFM0M3MiIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+6KeG6aKRPC90ZXh0Pgo8L3N2Zz4KPC9zdmc+" alt="系统架构图" class="system-image">
                </div>
                <div class="content-right">
                    <div class="feature-description">
                        <h3 style="font-size: 2em; margin-bottom: 20px; color: #00ff88;">🔍 全方位数据整合</h3>
                        <p style="font-size: 1.3em; line-height: 1.8; margin-bottom: 20px;">
                            系统集成融合了<span class="highlight">安检图像信息、旅客安检信息、货运运单信息、货运安检信息、安检视频监控信息</span>等多维度数据源
                        </p>
                        <p style="font-size: 1.3em; line-height: 1.8; margin-bottom: 20px;">
                            通过智能关联算法，实现<span class="highlight">行检、旅检、货检</span>三大业务安检过程信息的统一管理
                        </p>
                        <p style="font-size: 1.3em; line-height: 1.8;">
                            为安检业务监管、事后回溯与问题分析提供<span class="highlight">全面、准确、高效</span>的技术支撑
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第3页：核心功能 -->
        <div class="slide">
            <div class="logo">✈️ 机场安检管控平台</div>
            <div class="slide-number">3/6</div>
            <div class="slide-header">
                <h2 class="slide-title">核心功能特性</h2>
                <p class="slide-subtitle">四大核心能力，全面提升安检管理效率</p>
            </div>
            <div class="slide-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🔍</span>
                        <h3 class="feature-title">多维度查询检索</h3>
                        <p class="feature-description">
                            支持通过旅客信息、航班信息、安检通道等多种条件快速定位指定时间的安检情况，实现精准检索
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📸</span>
                        <h3 class="feature-title">X光图像回查</h3>
                        <p class="feature-description">
                            可提取查看旅客历史过检的随身行李X光机图像，为安检决策提供直观的视觉证据支持
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🎬</span>
                        <h3 class="feature-title">全流程复现</h3>
                        <p class="feature-description">
                            对旅客安检的全流程进行完整复现，从进入安检区到完成检查的每个环节都可追溯
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">👤</span>
                        <h3 class="feature-title">人员轨迹追踪</h3>
                        <p class="feature-description">
                            即使没有X光机接口，也能对人员进行一定范围的追溯，实现快速找人功能
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页：技术优势 -->
        <div class="slide">
            <div class="logo">✈️ 机场安检管控平台</div>
            <div class="slide-number">4/6</div>
            <div class="slide-header">
                <h2 class="slide-title">技术优势</h2>
                <p class="slide-subtitle">先进技术架构，保障系统稳定高效运行</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <ul class="value-list">
                        <li class="value-item">
                            <h3>🚀 高性能数据处理</h3>
                            <p>采用分布式架构，支持海量数据的实时处理和快速检索，响应时间控制在秒级</p>
                        </li>
                        <li class="value-item">
                            <h3>🔗 智能数据关联</h3>
                            <p>通过AI算法实现多源数据的智能关联，自动建立人员、物品、时间、地点的关联关系</p>
                        </li>
                        <li class="value-item">
                            <h3>🛡️ 安全可靠保障</h3>
                            <p>采用多层安全防护机制，确保敏感数据的安全性，支持权限分级管理</p>
                        </li>
                        <li class="value-item">
                            <h3>📱 灵活部署方案</h3>
                            <p>支持云端部署、本地部署、混合部署等多种方案，适应不同机场的IT环境需求</p>
                        </li>
                    </ul>
                </div>
                <div class="content-right">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDUwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iNDAwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkiIHJ4PSIxNSIvPgo8Y2lyY2xlIGN4PSIyNTAiIGN5PSIyMDAiIHI9IjEwMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDBGRjg4IiBzdHJva2Utd2lkdGg9IjMiLz4KPGC2lyY2xlIGN4PSIyNTAiIGN5PSIyMDAiIHI9IjE1MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyNTAiIGN5PSIyMDAiIHI9IjUwIiBmaWxsPSIjMDBGRjg4Ii8+Cjx0ZXh0IHg9IjI1MCIgeT0iMjA1IiBmaWxsPSIjMUUzQzcyIiBmb250LXNpemU9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXdlaWdodD0iYm9sZCI+QUk8L3RleHQ+Cjx0ZXh0IHg9IjI1MCIgeT0iNTAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjE4IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7mmbrog73ljJbmqKHlnos8L3RleHQ+Cjx0ZXh0IHg9IjUwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjE0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7mlbDmja7lhYPlkIg8L3RleHQ+Cjx0ZXh0IHg9IjQ1MCIgeT0iMjAwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5b+r6YCf5qOA57Si8L3RleHQ+Cjx0ZXh0IHg9IjI1MCIgeT0iMzUwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5YWo5rWB56iL5aSN546w8L3RleHQ+CjwvdGV4dD4KPC9zdmc+" alt="技术架构" class="system-image">
                </div>
            </div>
        </div>

        <!-- 第5页：应用价值 -->
        <div class="slide">
            <div class="logo">✈️ 机场安检管控平台</div>
            <div class="slide-number">5/6</div>
            <div class="slide-header">
                <h2 class="slide-title">核心价值</h2>
                <p class="slide-subtitle">显著提升安检管理效率，保障机场安全运营</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <div class="stats-container" style="flex-direction: column; gap: 20px;">
                        <div class="stat-item">
                            <span class="stat-number">90%</span>
                            <span class="stat-label">检索时间缩短</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">安检过程可追溯</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">全天候监控覆盖</span>
                        </div>
                    </div>
                </div>
                <div class="content-right">
                    <ul class="value-list">
                        <li class="value-item">
                            <h3>⚡ 极大缩短反查时间</h3>
                            <p>通过数据汇集和智能关联，实现快速反查定位，将原本需要数小时的查找工作缩短至分钟级</p>
                        </li>
                        <li class="value-item">
                            <h3>🔄 全流程可追溯</h3>
                            <p>对安检全过程进行完整记录，使每个环节都变得可追溯，为事后分析提供完整数据链</p>
                        </li>
                        <li class="value-item">
                            <h3>📊 业务全覆盖</h3>
                            <p>覆盖行检、旅检、货检三大业务场景，实现安检业务的全方位监管和管理</p>
                        </li>
                        <li class="value-item">
                            <h3>🎯 精准问题定位</h3>
                            <p>通过多维度数据关联分析，快速定位安检过程中的问题点，提升问题处理效率</p>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第6页：联系我们 -->
        <div class="slide">
            <div class="logo">✈️ 机场安检管控平台</div>
            <div class="slide-number">6/6</div>
            <div class="slide-header">
                <h2 class="slide-title">携手共建智慧机场</h2>
                <p class="slide-subtitle">专业团队 · 成熟方案 · 贴心服务</p>
            </div>
            <div class="slide-content">
                <div class="content-left">
                    <div class="feature-card" style="text-align: center; padding: 40px;">
                        <h3 style="font-size: 2em; margin-bottom: 30px; color: #00ff88;">🤝 合作优势</h3>
                        <div style="text-align: left;">
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>成熟产品</strong>：经过多个机场实际验证的成熟解决方案</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>专业团队</strong>：拥有丰富机场安检系统开发经验</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>定制服务</strong>：根据机场实际需求提供个性化定制</p>
                            <p style="font-size: 1.2em; margin-bottom: 15px;">✅ <strong>持续支持</strong>：提供7×24小时技术支持服务</p>
                        </div>
                    </div>
                </div>
                <div class="content-right">
                    <div class="feature-card" style="text-align: center; padding: 40px;">
                        <h3 style="font-size: 2em; margin-bottom: 30px; color: #00ff88;">📞 联系方式</h3>
                        <div style="text-align: left; font-size: 1.2em;">
                            <p style="margin-bottom: 20px;">
                                <strong>销售热线：</strong><br>
                                400-XXX-XXXX
                            </p>
                            <p style="margin-bottom: 20px;">
                                <strong>技术支持：</strong><br>
                                <EMAIL>
                            </p>
                            <p style="margin-bottom: 20px;">
                                <strong>商务合作：</strong><br>
                                <EMAIL>
                            </p>
                            <p>
                                <strong>公司地址：</strong><br>
                                北京市朝阳区XXX大厦XX层
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 1.5em; color: #00ff88; font-weight: bold;">
                    让我们一起打造更安全、更智能的机场安检环境！
                </p>
            </div>
        </div>

        <!-- 导航控制 -->
        <div class="navigation">
            <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
            <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
            <button class="nav-btn" onclick="toggleFullscreen()">全屏</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 触摸控制（移动设备）
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        }

        // 自动播放功能（可选）
        let autoPlay = false;
        let autoPlayInterval;

        function startAutoPlay() {
            if (autoPlay) return;
            autoPlay = true;
            autoPlayInterval = setInterval(nextSlide, 10000); // 10秒自动切换
        }

        function stopAutoPlay() {
            autoPlay = false;
            clearInterval(autoPlayInterval);
        }

        // 点击任意位置停止自动播放
        document.addEventListener('click', stopAutoPlay);
        document.addEventListener('keydown', stopAutoPlay);

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里启动自动播放
            // startAutoPlay();
        });
    </script>
</body>
</html>