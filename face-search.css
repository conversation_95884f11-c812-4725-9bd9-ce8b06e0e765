:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --bg-color: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem 0;
    box-shadow: var(--shadow);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.header-info {
    font-size: 1rem;
    opacity: 0.9;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 2rem;
}

/* 卡片样式 */
.upload-card, .status-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.upload-card h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1.5rem;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: var(--secondary-color);
    background-color: #f8faff;
}

.upload-area.dragover {
    border-color: var(--accent-color);
    background-color: #f0fdf4;
}

.upload-placeholder {
    text-align: center;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.upload-hint {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

.uploaded-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    object-fit: contain;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--text-secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-search {
    background-color: var(--accent-color);
    color: white;
    width: 100%;
    justify-content: center;
    font-size: 1rem;
    padding: 1rem;
}

.btn-search:hover:not(:disabled) {
    background-color: #059669;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.upload-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* 搜索控制 */
.search-controls {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.control-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.slider {
    width: 100%;
    margin-bottom: 0.5rem;
}

.select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
}

/* 状态卡片 */
.status-card {
    margin-top: 1rem;
    text-align: center;
}

.status-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.status-text {
    font-weight: 500;
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-color);
    width: 0%;
    transition: width 0.3s ease;
}

/* 结果区域 */
.results-section {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.results-header h2 {
    color: var(--text-primary);
    font-size: 1.25rem;
}

.results-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.results-container {
    max-height: 600px;
    overflow-y: auto;
}

.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 结果项 */
.result-item {
    display: flex;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.result-item:hover {
    box-shadow: var(--shadow);
    border-color: var(--secondary-color);
}

.result-avatar {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 1rem;
    border: 2px solid var(--border-color);
}

.result-info {
    flex: 1;
}

.result-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.result-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.result-similarity {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
}

.similarity-score {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.similarity-high { color: var(--accent-color); }
.similarity-medium { color: var(--warning-color); }
.similarity-low { color: var(--danger-color); }

.similarity-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }
    
    .header-content {
        padding: 0 1rem;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .upload-card, .results-section {
        padding: 1.5rem;
    }
}
