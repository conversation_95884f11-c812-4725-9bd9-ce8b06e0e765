// 模拟人员数据库
const mockDatabase = [
    {
        id: 1,
        name: "张伟",
        idCard: "110101199001011234",
        phone: "138****1234",
        department: "技术部",
        position: "高级工程师",
        avatar: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmciIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNGY0NmU1O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM3YzNhZWQ7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iODAiIGhlaWdodD0iODAiIGZpbGw9InVybCgjYmcpIiByeD0iOCIvPgogIDxjaXJjbGUgY3g9IjQwIiBjeT0iMzIiIHI9IjE2IiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjkiLz4KICA8cGF0aCBkPSJNMjAgNjggUTIwIDUyIDQwIDUyIFE2MCA1MiA2MCA2OCBMMjAgNjgiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuOSIvPgogIDx0ZXh0IHg9IjQwIiB5PSI3NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEwIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXdlaWdodD0iYm9sZCI+5byg5LyfPC90ZXh0Pgo8L3N2Zz4=",
        lastSeen: "2024-01-15 14:30:00",
        location: "A座大厅",
        status: "正常",
        category: "员工"
    },
    {
        id: 2,
        name: "李明",
        idCard: "110101199002021234",
        phone: "139****5678",
        department: "市场部",
        position: "市场经理",
        avatar: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmcyIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzA1OWY2OTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMTBiOTgxO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSJ1cmwoI2JnMikiIHJ4PSI4Ii8+CiAgPGNpcmNsZSBjeD0iNDAiIGN5PSIzMiIgcj0iMTYiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuOSIvPgogIDxwYXRoIGQ9Ik0yMCA2OCBRMjAgNTIgNDAgNTIgUTYwIDUyIDYwIDY4IEwyMCA2OCIgZmlsbD0iI2ZmZiIgb3BhY2l0eT0iMC45Ii8+CiAgPHRleHQgeD0iNDAiIHk9Ijc1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiNmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtd2VpZ2h0PSJib2xkIj7mnY7mmI48L3RleHQ+Cjwvc3ZnPg==",
        lastSeen: "2024-01-15 16:45:00",
        location: "B座会议室",
        status: "正常",
        category: "员工"
    },
    {
        id: 3,
        name: "王芳",
        idCard: "110101199003031234",
        phone: "137****9012",
        department: "人事部",
        position: "人事专员",
        avatar: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjODg4Ii8+CjxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjE1IiBmaWxsPSIjYWFhIi8+CjxwYXRoIGQ9Ik0yMCA2NSBRMjAgNTAgNDAgNTAgUTYwIDUwIDYwIDY1IEwyMCA2NSIgZmlsbD0iI2FhYSIvPgo8dGV4dCB4PSI0MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+546L6IqzPC90ZXh0Pgo8L3N2Zz4=",
        lastSeen: "2024-01-15 13:20:00",
        location: "C座办公区",
        status: "正常",
        category: "员工"
    },
    {
        id: 4,
        name: "陈强",
        idCard: "110101198504041234",
        phone: "135****3456",
        department: "安保部",
        position: "安保队长",
        avatar: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjMjIyIi8+CjxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjE1IiBmaWxsPSIjNTU1Ii8+CjxwYXRoIGQ9Ik0yMCA2NSBRMjAgNTAgNDAgNTAgUTYwIDUwIDYwIDY1IEwyMCA2NSIgZmlsbD0iIzU1NSIvPgo8dGV4dCB4PSI0MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+6ZmI5by6PC90ZXh0Pgo8L3N2Zz4=",
        lastSeen: "2024-01-15 18:00:00",
        location: "主入口",
        status: "在岗",
        category: "安保"
    },
    {
        id: 5,
        name: "刘访客",
        idCard: "110101199505051234",
        phone: "136****7890",
        department: "外部访客",
        position: "客户代表",
        avatar: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjYjMzIi8+CjxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjE1IiBmaWxsPSIjZDU1Ii8+CjxwYXRoIGQ9Ik0yMCA2NSBRMjAgNTAgNDAgNTAgUTYwIDUwIDYwIDY1IEwyMCA2NSIgZmlsbD0iI2Q1NSIvPgo8dGV4dCB4PSI0MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5YiY6K6/5a6iPC90ZXh0Pgo8L3N2Zz4=",
        lastSeen: "2024-01-15 15:30:00",
        location: "访客接待区",
        status: "访问中",
        category: "访客"
    }
];

class FaceSearchSystem {
    constructor() {
        this.uploadedImage = null;
        this.isSearching = false;
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // 获取DOM元素
        this.uploadArea = document.getElementById('uploadArea');
        this.uploadPlaceholder = document.getElementById('uploadPlaceholder');
        this.uploadedImageEl = document.getElementById('uploadedImage');
        this.fileInput = document.getElementById('fileInput');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.searchBtn = document.getElementById('searchBtn');
        this.statusCard = document.getElementById('statusCard');
        this.statusIcon = document.getElementById('statusIcon');
        this.statusText = document.getElementById('statusText');
        this.progressFill = document.getElementById('progressFill');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.noResults = document.getElementById('noResults');
        this.resultsStats = document.getElementById('resultsStats');
        this.matchCount = document.getElementById('matchCount');
        this.searchTime = document.getElementById('searchTime');
        this.similarityThreshold = document.getElementById('similarityThreshold');
        this.thresholdValue = document.getElementById('thresholdValue');
        this.searchScope = document.getElementById('searchScope');
        this.detailModal = document.getElementById('detailModal');
        this.modalBody = document.getElementById('modalBody');
        this.closeModal = document.getElementById('closeModal');
    }

    bindEvents() {
        // 文件上传相关事件
        this.uploadBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.uploadArea.addEventListener('dragleave', () => this.uploadArea.classList.remove('dragover'));
        this.clearBtn.addEventListener('click', () => this.clearImage());

        // 搜索相关事件
        this.searchBtn.addEventListener('click', () => this.startSearch());
        this.similarityThreshold.addEventListener('input', (e) => {
            this.thresholdValue.textContent = e.target.value + '%';
        });

        // 模态框事件
        this.closeModal.addEventListener('click', () => this.closeDetailModal());
        this.detailModal.addEventListener('click', (e) => {
            if (e.target === this.detailModal) this.closeDetailModal();
        });
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件！');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.uploadedImage = e.target.result;
            this.displayUploadedImage();
        };
        reader.readAsDataURL(file);
    }

    displayUploadedImage() {
        this.uploadedImageEl.src = this.uploadedImage;
        this.uploadedImageEl.style.display = 'block';
        this.uploadPlaceholder.style.display = 'none';
        this.clearBtn.style.display = 'inline-block';
        this.searchBtn.disabled = false;
    }

    clearImage() {
        this.uploadedImage = null;
        this.uploadedImageEl.style.display = 'none';
        this.uploadPlaceholder.style.display = 'block';
        this.clearBtn.style.display = 'none';
        this.searchBtn.disabled = true;
        this.fileInput.value = '';
        this.hideStatus();
        this.showNoResults();
    }

    async startSearch() {
        if (!this.uploadedImage || this.isSearching) return;

        this.isSearching = true;
        this.searchBtn.disabled = true;
        this.showStatus();

        // 模拟搜索过程
        await this.simulateSearch();

        this.isSearching = false;
        this.searchBtn.disabled = false;
        this.hideStatus();
    }

    async simulateSearch() {
        const steps = [
            { text: '正在分析人脸特征...', progress: 20 },
            { text: '正在匹配数据库...', progress: 50 },
            { text: '正在计算相似度...', progress: 80 },
            { text: '正在生成结果...', progress: 100 }
        ];

        for (let step of steps) {
            this.updateStatus('⏳', step.text, step.progress);
            await this.delay(800);
        }

        // 生成模拟结果
        const results = this.generateMockResults();
        this.displayResults(results);
    }

    generateMockResults() {
        const threshold = parseInt(this.similarityThreshold.value);
        const scope = this.searchScope.value;

        // 根据搜索范围过滤数据
        let filteredData = mockDatabase;
        if (scope === 'recent') {
            filteredData = mockDatabase.slice(0, 3);
        } else if (scope === 'vip') {
            filteredData = mockDatabase.filter(p => p.category === 'employee');
        } else if (scope === 'blacklist') {
            filteredData = [];
        }

        // 生成随机相似度分数
        const results = filteredData.map(person => ({
            ...person,
            similarity: Math.floor(Math.random() * (99 - threshold) + threshold)
        })).filter(person => person.similarity >= threshold)
          .sort((a, b) => b.similarity - a.similarity);

        return results;
    }

    displayResults(results) {
        const searchTime = (Math.random() * 2 + 0.5).toFixed(2);

        if (results.length === 0) {
            this.showNoResults('未找到匹配的人员');
            return;
        }

        this.matchCount.textContent = results.length;
        this.searchTime.textContent = searchTime;
        this.resultsStats.style.display = 'flex';

        this.resultsContainer.innerHTML = '';
        results.forEach(person => {
            const resultItem = this.createResultItem(person);
            this.resultsContainer.appendChild(resultItem);
        });
    }

    createResultItem(person) {
        const item = document.createElement('div');
        item.className = 'result-item';
        item.onclick = () => this.showPersonDetail(person);

        const similarityClass = person.similarity >= 90 ? 'similarity-high' :
                               person.similarity >= 80 ? 'similarity-medium' : 'similarity-low';

        item.innerHTML = `
            <img src="${person.avatar}" alt="${person.name}" class="result-avatar">
            <div class="result-info">
                <div class="result-name">${person.name}</div>
                <div class="result-details">
                    <div>身份证: ${person.idCard}</div>
                    <div>部门: ${person.department}</div>
                    <div>职位: ${person.position}</div>
                    <div>最后出现: ${person.lastSeen}</div>
                    <div>位置: ${person.location}</div>
                    <div>状态: ${person.status}</div>
                </div>
            </div>
            <div class="result-similarity">
                <div class="similarity-score ${similarityClass}">${person.similarity}%</div>
                <div class="similarity-label">相似度</div>
            </div>
        `;

        return item;
    }

    showPersonDetail(person) {
        this.modalBody.innerHTML = `
            <div style="display: flex; gap: 2rem; margin-bottom: 2rem;">
                <img src="${person.avatar}" alt="${person.name}" style="width: 120px; height: 120px; border-radius: 8px; object-fit: cover;">
                <div style="flex: 1;">
                    <h3 style="margin-bottom: 1rem; color: var(--text-primary);">${person.name}</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; font-size: 0.9rem;">
                        <div><strong>身份证:</strong> ${person.idCard}</div>
                        <div><strong>手机号:</strong> ${person.phone}</div>
                        <div><strong>部门:</strong> ${person.department}</div>
                        <div><strong>职位:</strong> ${person.position}</div>
                        <div><strong>类别:</strong> ${person.category}</div>
                        <div><strong>状态:</strong> ${person.status}</div>
                    </div>
                </div>
            </div>
            <div style="border-top: 1px solid var(--border-color); padding-top: 1rem;">
                <h4 style="margin-bottom: 1rem;">最近活动记录</h4>
                <div style="background: var(--bg-color); padding: 1rem; border-radius: 6px;">
                    <div style="margin-bottom: 0.5rem;"><strong>最后出现时间:</strong> ${person.lastSeen}</div>
                    <div style="margin-bottom: 0.5rem;"><strong>最后出现位置:</strong> ${person.location}</div>
                    <div><strong>活动状态:</strong> ${person.status}</div>
                </div>
            </div>
        `;
        this.detailModal.style.display = 'block';
    }

    closeDetailModal() {
        this.detailModal.style.display = 'none';
    }

    showStatus() {
        this.statusCard.style.display = 'block';
    }

    hideStatus() {
        this.statusCard.style.display = 'none';
    }

    updateStatus(icon, text, progress) {
        this.statusIcon.textContent = icon;
        this.statusText.textContent = text;
        this.progressFill.style.width = progress + '%';
    }

    showNoResults(message = '请上传照片开始查询') {
        this.resultsStats.style.display = 'none';
        this.resultsContainer.innerHTML = `
            <div class="no-results">
                <div class="no-results-icon">🔍</div>
                <p>${message}</p>
            </div>
        `;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化系统
document.addEventListener('DOMContentLoaded', () => {
    new FaceSearchSystem();
});
